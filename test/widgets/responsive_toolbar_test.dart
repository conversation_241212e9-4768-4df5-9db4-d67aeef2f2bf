import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sintesy_me_app/widgets/custom_bottom_navigation_bar.dart';
import 'package:sintesy_me_app/themes/light_theme.dart';

void main() {
  group('CustomBottomNavigationBar Responsive Tests', () {
    testWidgets('shows all buttons on large screens (>=400px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme,
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(450, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar todos os 5 botões
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsOneWidget);
      expect(find.text('ANOTAÇÃO'), findsOneWidget);
      expect(find.text('ARQUIVO'), findsOneWidget);
      expect(find.text('MAIS'), findsNothing);
    });

    testWidgets('shows compact toolbar on medium screens (350-399px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme,
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(375, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar 3 botões principais + menu "MAIS"
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsOneWidget);
      expect(find.text('MAIS'), findsOneWidget);
      expect(find.text('ANOTAÇÃO'), findsNothing); // Deve estar no menu
      expect(find.text('ARQUIVO'), findsNothing); // Deve estar no menu
    });

    testWidgets('shows minimal toolbar on small screens (<350px)', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme,
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(320, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () {},
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Deve mostrar apenas 2 botões principais + menu "MAIS"
      expect(find.text('GRAVAÇÃO'), findsOneWidget);
      expect(find.text('YOUTUBE'), findsOneWidget);
      expect(find.text('MAIS'), findsOneWidget);
      expect(find.text('REUNIÃO'), findsNothing); // Deve estar no menu
      expect(find.text('ANOTAÇÃO'), findsNothing); // Deve estar no menu
      expect(find.text('ARQUIVO'), findsNothing); // Deve estar no menu
    });

    testWidgets('menu "MAIS" funciona corretamente', (WidgetTester tester) async {
      bool noteTapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: lightTheme,
          home: Scaffold(
            body: MediaQuery(
              data: const MediaQueryData(size: Size(320, 800)),
              child: CustomBottomNavigationBar(
                onRecordTap: () {},
                onYoutubeTap: () {},
                onNoteTap: () => noteTapped = true,
                onFileTap: () {},
                onMeetingTap: () {},
              ),
            ),
          ),
        ),
      );

      // Toca no botão "MAIS"
      await tester.tap(find.text('MAIS'));
      await tester.pumpAndSettle();

      // Verifica se o menu apareceu
      expect(find.text('ANOTAÇÃO'), findsOneWidget);
      
      // Toca na opção "ANOTAÇÃO"
      await tester.tap(find.text('ANOTAÇÃO'));
      await tester.pumpAndSettle();

      // Verifica se a função foi chamada
      expect(noteTapped, isTrue);
    });
  });
}
