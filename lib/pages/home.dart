import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/pages/anotation.dart';
import 'package:sintesy_app/pages/sintesy_viewer.dart';
import 'package:sintesy_app/providers/recording_state.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/chat_rest.dart';
import 'package:sintesy_app/providers/youtube_state.dart';
import 'package:sintesy_app/services/clipboard_service.dart';
import 'package:sintesy_app/services/handle_task_service.dart';
import 'package:sintesy_app/services/permission_service.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_service.dart';
import 'package:sintesy_app/services/sintesy_sync_service.dart';
import 'package:sintesy_app/services/user_plan_manager.dart';
import 'package:sintesy_app/services/whatsapp_service.dart';
import 'package:sintesy_app/utils/call_plan.dart';
import 'package:sintesy_app/widgets/custom_bottom_navigation_bar.dart';
import 'package:sintesy_app/widgets/list_sintesy.dart';
import 'package:sintesy_app/widgets/recorder.dart';
import 'package:sintesy_app/widgets/select_file.dart';
import 'package:sintesy_app/widgets/whatsapp_dialog.dart';
import 'package:sintesy_app/widgets/youtube_download.dart';
import 'package:sintesy_app/widgets/meeting_link_input.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  // Removed _selectedIndex as it's no longer needed
  ValueNotifier<List<SintesyModel>> recordingsNotifier = ValueNotifier([]);
  final TextEditingController controller = TextEditingController();
  late PermissionService permissionService;
  late RecordingState recordingState;
  late SintesyService sintesyService;
  late SintesySyncService sintesySyncService;
  final SintesyCrud sintesyCrud = SintesyCrud();
  late UserPlanProvider userPlanProvider;
  late StreamSubscription<bool> keyboardSubscription;
  late YouTubeState youtubeState;
  final ClipboardService _clipboardService = ClipboardService();

  String? _detectedYoutubeLink;
  bool _showYoutubeLinkButton = false;

  final TextEditingController _expandedTextController = TextEditingController();
  final FocusNode _expandedTextFieldFocus = FocusNode();

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearchVisible = false;
  final FocusNode _searchFocusNode = FocusNode();

  final TextEditingController _chatInputController = TextEditingController();
  bool _isLoading = false;
  String _loadingText = "Buscando anotações...";
  late Timer _loadingTextTimer;
  bool _isSendButtonVisible = false;

  final List<String> _loadingTexts = [
    "Buscando anotações...",
    "Pegando trechos...",
    "Pensando sobre...",
    "Escrevendo...",
  ];
  final List<IconData> _loadingIcons = [
    Icons.search,
    Icons.snippet_folder,
    Icons.psychology,
    Icons.edit,
  ];
  int _currentLoadingTextIndex = 0;

  @override
  void initState() {
    super.initState();
    sintesySyncService =
        Provider.of<SintesySyncService>(context, listen: false);
    recordingState = Provider.of<RecordingState>(context, listen: false);
    permissionService = PermissionService(context);
    sintesyService = SintesyService();
    userPlanProvider = Provider.of<UserPlanProvider>(context, listen: false);
    userPlanProvider.startPlanUpdates();
    youtubeState = Provider.of<YouTubeState>(context, listen: false);
    WidgetsBinding.instance.addObserver(this);
    _expandedTextController.addListener(_updateSendButtonVisibility);
    _searchController.addListener(_onSearchChanged);
    _loadingTextTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_isLoading) {
        setState(() {
          _currentLoadingTextIndex =
              (_currentLoadingTextIndex + 1) % _loadingTexts.length;
          _loadingText = _loadingTexts[_currentLoadingTextIndex];
        });
      }
    });
    FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      taskInitService();
      sintesySyncService.syncSintesysTimer(context);
      await Future.delayed(const Duration(milliseconds: 500));
      await _checkSintesysCount();
      await _checkClipboardForYoutubeLink();
      if (mounted) {
        WhatsAppService.checkAndShowWhatsAppDialog(context);
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkClipboardForYoutubeLink();
    }
  }

  Future<void> _checkClipboardForYoutubeLink() async {
    final youtubeLink = await _clipboardService.getYouTubeLinkFromClipboard();
    if (youtubeLink != null) {
      setState(() {
        _detectedYoutubeLink = youtubeLink;
        _showYoutubeLinkButton = true;
      });
    }
  }

  Future<void> _checkSintesysCount() async {
    try {
      final sintesyState = Provider.of<SintesyState>(context, listen: false);
      await sintesyState.sintesysRefresh();
    } catch (e) {}
  }

  void _onReceiveTaskData(Object data) {}

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    FlutterForegroundTask.removeTaskDataCallback(_onReceiveTaskData);
    _expandedTextController.removeListener(_updateSendButtonVisibility);
    _expandedTextController.dispose();
    _expandedTextFieldFocus.dispose();
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    _chatInputController.dispose();
    _loadingTextTimer.cancel();
    super.dispose();
  }

  void _handleDetectedYoutubeLink() {
    if (_detectedYoutubeLink != null) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              top: 20,
              left: 20,
              right: 20,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "Link do Youtube detectado",
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                YouTubeLinkInput(initialLink: _detectedYoutubeLink),
                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ).then((_) {
        setState(() {
          _detectedYoutubeLink = null;
          _showYoutubeLinkButton = false;
        });
        _clipboardService.clearClipboard();
      });
    }
  }

  void _updateSendButtonVisibility() {
    setState(() {
      _isSendButtonVisible = _expandedTextController.text.isNotEmpty;
    });
  }

  void _handleRecordTap() {
    final recordingState = Provider.of<RecordingState>(context, listen: false);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: recordingState.recorderStatus == 'stop',
      enableDrag: recordingState.recorderStatus == 'stop',
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Consumer<RecordingState>(
              builder: (context, recordingState, child) {
                return PopScope(
                  canPop: recordingState.recorderStatus == 'stop',
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom + 20,
                      top: 20,
                      left: 20,
                      right: 20,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          "Gravar áudio",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 20),
                        const Recorder(),
                        const SizedBox(height: 10),
                        if (recordingState.recorderStatus != 'stop')
                          const Text(
                            "Não é possível fechar essa aba durante a gravação",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.black26,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  void _handleYoutubeTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            top: 20,
            left: 20,
            right: 20,
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Link do Youtube",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              YouTubeLinkInput(),
              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _handleNoteTap() {
    _toggleTextField();
  }

  void _handleFileTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            top: 20,
            left: 20,
            right: 20,
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Selecionar arquivo",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              AudioFileSelector(),
              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _handleMeetingTap() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            top: 20,
            left: 20,
            right: 20,
          ),
          child: const Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "Criar Sintesy de Reunião",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              MeetingLinkInput(),
              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  void _toggleTextField() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteScreen(controller: _expandedTextController),
      ),
    );
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  void _forceShowKeyboard() {
    _searchFocusNode.requestFocus();
  }

  void _toggleSearchVisibility() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (_isSearchVisible) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          FocusScope.of(context).requestFocus(_searchFocusNode);
          Future.delayed(const Duration(milliseconds: 100), () {
            _forceShowKeyboard();
          });
        });
      } else {
        _searchQuery = '';
        _searchController.clear();
        _searchFocusNode.unfocus();
      }
    });
  }

  Widget _buildSearchField() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });

    return TextField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      decoration: const InputDecoration(
        hintText: 'Pesquisar...',
        border: InputBorder.none,
      ),
      style: const TextStyle(),
      onChanged: (query) {},
      showCursor: true,
      autofocus: true,
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
    );
  }

  Future<void> _askQuestion(String question) async {
    setState(() {
      _isLoading = true;
      _currentLoadingTextIndex = 0;
      _loadingText = _loadingTexts[_currentLoadingTextIndex];
    });

    final responseData = await SintesyChatAPI().askSintesy(question);
    _showChatResponse(responseData['answer'], responseData['sources']);

    setState(() {
      _isLoading = false;
    });
  }

  void _showAIChatDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom + 20,
                top: 20,
                left: 16,
                right: 16,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    "Pergunte à IA",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _isLoading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(_loadingIcons[_currentLoadingTextIndex]),
                            const SizedBox(width: 16),
                            Flexible(
                              child: AnimatedTextKit(
                                animatedTexts: [
                                  TypewriterAnimatedText(
                                    _loadingText,
                                    textStyle: const TextStyle(fontSize: 16.0),
                                    speed: const Duration(milliseconds: 10),
                                  ),
                                ],
                                isRepeatingAnimation: true,
                                repeatForever: true,
                                pause: const Duration(milliseconds: 2000),
                              ),
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _chatInputController,
                                decoration: InputDecoration(
                                  hintText: 'Pergunte sobre suas anotações...',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                    borderSide: BorderSide.none,
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[100],
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 12.0),
                                ),
                                onSubmitted: (value) {
                                  if (value.isNotEmpty) {
                                    Navigator.pop(context);
                                    _askQuestion(value);
                                    _chatInputController.clear();
                                  }
                                },
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.send_rounded,
                                  color: Colors.blue),
                              onPressed: () {
                                if (_chatInputController.text.isNotEmpty) {
                                  Navigator.pop(context);
                                  _askQuestion(_chatInputController.text);
                                  _chatInputController.clear();
                                }
                              },
                            ),
                          ],
                        ),
                  const SizedBox(height: 20),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showChatResponse(String response, List<dynamic> sources) {
    final decodedResponse = response;
    final TextEditingController responseController =
        TextEditingController(text: decodedResponse);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  child: Expanded(
                    child: SingleChildScrollView(
                      child: MarkdownBody(data: decodedResponse),
                    ),
                  ),
                ),
                const SizedBox(height: 5),
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              NoteScreen(controller: responseController),
                        ),
                      );
                    },
                    child: const Text('Copiar para Anotação'),
                  ),
                ),
                if (sources.isNotEmpty) ...[
                  const SizedBox(height: 5),
                  const Text("Fontes:",
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(
                    height: 40,
                    child: ListView.separated(
                      scrollDirection: Axis.horizontal,
                      itemCount: sources.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(width: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      itemBuilder: (context, index) {
                        final source = sources[index];
                        return InkWell(
                          onTap: () {
                            final currentContext = context;
                            Navigator.pop(currentContext);
                            String? sourceId = source['id'].toString();
                            _openSintesyViewer(sourceId);
                          },
                          child: Chip(
                            label: ConstrainedBox(
                              constraints: const BoxConstraints(maxWidth: 150),
                              child: Text(
                                source['name'] ?? 'Fonte Desconhecida',
                                overflow: TextOverflow.fade,
                              ),
                            ),
                            backgroundColor:
                                Theme.of(context).colorScheme.surface,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _openSintesyViewer(String sourceId) async {
    SintesyModel? sintesyModel = await sintesyCrud.getById(sourceId);
    if (sintesyModel != null) {
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SintesyViewer(sintesy: sintesyModel),
          ),
        );
      }
    } else {
      _showSnackBar("Sintesy não encontrada localmente.");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _isSearchVisible ? _buildSearchField() : Text('Todas as Sintesys', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        automaticallyImplyLeading: true,
        actions: [
          Consumer<UserPlanProvider>(
            builder: (context, userPlanProvider, child) {
              final userPlan = userPlanProvider.userPlan;
              if (userPlan != null && userPlan.isPremium == false) {
                return InkWell(
                  onTap: () {
                    callPlan(context);
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.rocket_launch,
                            color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 4),
                        Text(
                          "Seja Pro",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              } else {
                return const SizedBox();
              }
            },
          ),
          Container(
            width: 40,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                  color: Theme.of(context).colorScheme.primary, width: 2),
            ),
            child: TextButton(
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                shape: const CircleBorder(),
              ),
              onPressed: () {
                _showAIChatDialog();
              },
              child: const Text("AI",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17)),
            ),
          ),
          if (_isSearchVisible)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () {
                _toggleSearchVisibility();
              },
            )
          else
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                _toggleSearchVisibility();
                Future.delayed(const Duration(milliseconds: 200), () {
                  _forceShowKeyboard();
                });
              },
            ),
        ],
      ),
      drawer: Drawer(
        shape: const RoundedRectangleBorder(),
        width: 250,
        backgroundColor: Colors.black,
        child: Column(
          children: <Widget>[
            SizedBox(
              height: 120,
              child: DrawerHeader(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                child: Text(
                  '',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSecondary,
                    fontSize: 24,
                  ),
                ),
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: <Widget>[
                  ListTile(
                    iconColor: Colors.grey,
                    textColor: Theme.of(context).colorScheme.onSurface,
                    leading: const FaIcon(FontAwesomeIcons.whatsapp,
                        color: Color(0xFF25D366)),
                    title: const Text('Integrar WhatsApp'),
                    onTap: () {
                      Navigator.pop(context);
                      _openWhatsAppDialog();
                    },
                  ),
                  ListTile(
                    iconColor: Colors.grey,
                    textColor: Theme.of(context).colorScheme.onSurface,
                    leading: const Icon(FontAwesomeIcons.message),
                    title: const Text('Suporte'),
                    onTap: () {
                      Navigator.pop(context);
                      _openSupportWebsite();
                    },
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: ListTile(
                iconColor: Colors.grey,
                textColor: Theme.of(context).colorScheme.onSurface,
                leading: const Icon(Icons.exit_to_app),
                title: const Text('Sair'),
                onTap: () {
                  logOut();
                },
              ),
            ),
          ],
        ),
      ),
      body: Stack(
        children: [
          ListSintesy(searchQuery: _searchQuery),
          if (_showYoutubeLinkButton)
            Positioned(
              top: 20,
              left: 20,
              right: 20,
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                color: Theme.of(context).colorScheme.surface,
                child: InkWell(
                  onTap: _handleDetectedYoutubeLink,
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        const Icon(
                          FontAwesomeIcons.youtube,
                          color: Colors.red,
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            'Link do YouTube detectado. Toque para gerar Sintesy',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _detectedYoutubeLink = null;
                              _showYoutubeLinkButton = false;
                            });
                            _clipboardService.clearClipboard();
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: CustomBottomNavigationBar(
              onRecordTap: _handleRecordTap,
              onYoutubeTap: _handleYoutubeTap,
              onNoteTap: _handleNoteTap,
              onFileTap: _handleFileTap,
              onMeetingTap: _handleMeetingTap,
            ),
          ),
        ],
      ),
    );
  }

  void _openWhatsAppDialog() {
    final userPlanProvider =
        Provider.of<UserPlanProvider>(context, listen: false);
    final userPlan = userPlanProvider.userPlan;

    final hasWhatsAppNumber = userPlan != null &&
        userPlan.whatsapp != null &&
        userPlan.whatsapp!.isNotEmpty;

    showDialog(
      context: context,
      barrierDismissible: hasWhatsAppNumber,
      builder: (context) => const WhatsAppDialog(),
    );
  }

  void _openSupportWebsite() async {
    final Uri url = Uri.parse('https://sintesy.me/');
    if (!await url_launcher.launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  Future<void> logOut() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove("token");
    sintesyCrud.clearAll();
    if (mounted) {
      Navigator.of(context)
          .pushNamedAndRemoveUntil('/login', (Route<dynamic> route) => false);
    }
    sintesySyncService.stopSyncSintesys();
  }
}
