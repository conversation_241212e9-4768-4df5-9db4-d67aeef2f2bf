import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:sintesy_app/models/sintesy_model.dart';
import 'package:sintesy_app/providers/sintesy_state.dart';
import 'package:sintesy_app/services/meeting_client.dart';
import 'package:sintesy_app/services/sintesy_local_crud.dart';
import 'package:sintesy_app/services/sintesy_service.dart';

class MeetingLinkInput extends StatefulWidget {
  final String? initialLink;

  const MeetingLinkInput({super.key, this.initialLink});

  @override
  State<MeetingLinkInput> createState() => _MeetingLinkInputState();
}

class _MeetingLinkInputState extends State<MeetingLinkInput> {
  final TextEditingController _urlController = TextEditingController();
  final MeetingAPI _meetingAPI = MeetingAPI();
  final SintesyCrud _sintesyCrud = SintesyCrud();
  final SintesyService _sintesyService = SintesyService();
  
  bool _isLoading = false;
  bool _showAdvancedOptions = false;
  File? _selectedImage;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    if (widget.initialLink != null) {
      _urlController.text = widget.initialLink!;
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );
      
      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      _showSnackBar('Erro ao selecionar imagem: $e');
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  Future<void> _createMeetingSintesy() async {
    final url = _urlController.text.trim();
    
    if (url.isEmpty) {
      _showSnackBar('Por favor, insira o link da reunião');
      return;
    }

    if (!MeetingAPI.isValidMeetingUrl(url)) {
      _showSnackBar('Link inválido. Use apenas links do Google Meet ou Microsoft Teams');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String? botImageType;
      String? botImageData;
      
      // Processa a imagem se selecionada
      if (_selectedImage != null) {
        final bytes = await _selectedImage!.readAsBytes();
        botImageData = base64Encode(bytes);
        
        final extension = _selectedImage!.path.split('.').last.toLowerCase();
        botImageType = 'image/$extension';
      }

      // Chama a API para criar a sintesy
      final response = await _meetingAPI.createFromMeeting(
        meetingUrl: url,
        botImageType: botImageType,
        botImageData: botImageData,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        
        // Cria o modelo local da sintesy
        final attendeeBot = AttendeeBotModel.fromJson(responseData['attendee_bot'] ?? {});
        
        final sintesy = SintesyModel(
          name: responseData['name'] ?? 'Gravação de Reunião',
          createdDate: DateTime.now(),
          source: MeetingAPI.getMeetingType(url),
          status: responseData['status'],
          attendeeBot: attendeeBot,
        );

        // Salva localmente
        await _sintesyCrud.add(sintesy);
        
        // Atualiza o estado
        final sintesyState = Provider.of<SintesyState>(context, listen: false);
        sintesyState.sintesys = await _sintesyService.getAll();
        sintesyState.notifyListeners();

        if (mounted) {
          Navigator.pop(context);
          _showSnackBar('Sintesy de reunião criada com sucesso!');
        }
      } else {
        _showSnackBar('Erro ao criar sintesy: ${response.statusCode}');
      }
    } catch (e) {
      _showSnackBar('Erro ao criar sintesy: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _urlController,
                decoration: const InputDecoration(
                  hintText: 'Cole o link da reunião (Meet ou Teams)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.url,
                enabled: !_isLoading,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: _isLoading ? null : () {
                setState(() {
                  _showAdvancedOptions = !_showAdvancedOptions;
                });
              },
              icon: Icon(
                Icons.settings,
                color: _showAdvancedOptions ? Theme.of(context).primaryColor : null,
              ),
            ),
          ],
        ),
        
        if (_showAdvancedOptions) ...[
          const SizedBox(height: 16),
          const Text(
            'Configurações Avançadas',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          
          // Seção de imagem do bot
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Avatar do Bot (Opcional)'),
                const SizedBox(height: 8),
                if (_selectedImage != null) ...[
                  Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImage!,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Imagem selecionada',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      ),
                      IconButton(
                        onPressed: _removeImage,
                        icon: const Icon(Icons.close, color: Colors.red),
                      ),
                    ],
                  ),
                ] else ...[
                  OutlinedButton.icon(
                    onPressed: _isLoading ? null : _pickImage,
                    icon: const Icon(Icons.image),
                    label: const Text('Selecionar Imagem'),
                  ),
                ],
              ],
            ),
          ),
        ],
        
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: _isLoading ? null : _createMeetingSintesy,
          child: _isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Criar Sintesy da Reunião'),
        ),
      ],
    );
  }
}
