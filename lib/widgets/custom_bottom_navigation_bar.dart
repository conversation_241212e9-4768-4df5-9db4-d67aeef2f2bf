import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../themes/light_theme.dart';

class CustomBottomNavigationBar extends StatelessWidget {
  final Function onRecordTap;
  final Function onYoutubeTap;
  final Function onNoteTap;
  final Function onFileTap;
  final Function onMeetingTap;

  const CustomBottomNavigationBar({
    super.key,
    required this.onRecordTap,
    required this.onYoutubeTap,
    required this.onNoteTap,
    required this.onFileTap,
    required this.onMeetingTap,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>();

    return KeyboardVisibilityBuilder(
      builder: (context, isKeyboardVisible) {
        if (isKeyboardVisible) {
          return const SizedBox.shrink();
        }

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                appColors?.containerGradientStart ?? Theme.of(context).colorScheme.surface,
                (appColors?.containerGradientEnd ?? Theme.of(context).colorScheme.surface).withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(50),
            boxShadow: [
              BoxShadow(
                color: appColors?.shadowColor ?? Colors.black12,
                spreadRadius: 1,
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: (appColors?.borderColor ?? Colors.grey).withOpacity(0.15),
              width: 1.0,
            ),
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 5.0, horizontal: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildToolbarButton(
                  context: context,
                  label: 'GRAVAÇÃO',
                  icon: FontAwesomeIcons.microphone,
                  onTap: () => onRecordTap(),
                ),
                _buildVerticalDivider(context),
                _buildToolbarButton(
                  context: context,
                  label: 'YOUTUBE',
                  icon: FontAwesomeIcons.youtube,
                  onTap: () => onYoutubeTap(),
                ),
                _buildVerticalDivider(context),
                _buildToolbarButton(
                  context: context,
                  label: 'REUNIÃO',
                  icon: FontAwesomeIcons.video,
                  onTap: () => onMeetingTap(),
                ),
                _buildVerticalDivider(context),
                _buildToolbarButton(
                  context: context,
                  label: 'ANOTAÇÃO',
                  icon: FontAwesomeIcons.penToSquare,
                  onTap: () => onNoteTap(),
                ),
                _buildVerticalDivider(context),
                _buildToolbarButton(
                  context: context,
                  label: 'ARQUIVO',
                  icon: FontAwesomeIcons.fileArrowUp,
                  onTap: () => onFileTap(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildToolbarButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required Function onTap,
  }) {
    final Color iconColor = Theme.of(context).colorScheme.secondary;
    final Color textColor = Theme.of(context).colorScheme.tertiary;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => onTap(),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(5),
                  child: Icon(
                    icon,
                    color: iconColor,
                    size: 18,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10,
                    color: textColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVerticalDivider(BuildContext context) {
    final appColors = Theme.of(context).extension<AppColors>()!;

    return Container(
      height: 24,
      width: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: appColors.dividerColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(5),
      ),
    );
  }
}
